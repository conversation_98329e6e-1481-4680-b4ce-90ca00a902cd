# TVBOX Supabase 設置檢查清單

## ✅ 已完成的設置

- [x] 安裝 Supabase 客戶端套件
- [x] 創建 Supabase 配置檔案
- [x] 設定環境變數
- [x] 建立資料庫服務層
- [x] 實作用戶認證系統
- [x] 創建雲端同步功能
- [x] 更新 UI 組件
- [x] 開發伺服器已啟動在 http://localhost:5174/

## 🔧 需要您完成的步驟

### 1. 設定 Supabase 資料庫架構
在 Supabase Dashboard 中執行以下步驟：

1. 前往 [Supabase Dashboard](https://app.supabase.com)
2. 選擇您的專案 `pqwvmcmmqjihoaixqlsz`
3. 前往 **SQL Editor**
4. 複製 `database/schema.sql` 檔案的完整內容
5. 貼上到 SQL Editor 並點擊 **Run**

### 2. 驗證 Anon Key（重要）
目前使用的 anon key 可能不正確，請驗證：

1. 在 Supabase Dashboard 中前往 **Settings > API**
2. 複製 **anon public** key
3. 更新 `.env` 檔案中的 `VITE_SUPABASE_ANON_KEY`

### 3. 測試功能
完成上述步驟後，測試以下功能：

1. **註冊新用戶**
   - 前往 http://localhost:5174/register
   - 填寫註冊表單
   - 檢查是否成功創建帳戶

2. **登入功能**
   - 前往 http://localhost:5174/login
   - 使用註冊的帳戶登入
   - 檢查導航欄是否顯示用戶名

3. **資料同步**
   - 登入後載入一個 M3U 播放清單
   - 收藏一些頻道
   - 檢查資料是否儲存到 Supabase

## 🔍 故障排除

### 常見問題

**1. 連接錯誤**
```
Error: Invalid API key
```
**解決方案**: 檢查 `.env` 檔案中的 `VITE_SUPABASE_ANON_KEY` 是否正確

**2. 資料表不存在**
```
relation "users" does not exist
```
**解決方案**: 在 Supabase SQL Editor 中執行 `database/schema.sql`

**3. 認證失敗**
```
Email not confirmed
```
**解決方案**: 
- 檢查 Supabase Dashboard > Authentication > Settings
- 確認 "Enable email confirmations" 設定
- 或暫時關閉電子郵件確認功能進行測試

**4. RLS 權限錯誤**
```
Row Level Security policy violation
```
**解決方案**: 確認已執行完整的 `schema.sql`，包含所有 RLS 政策

## 🧪 測試命令

您可以使用以下命令測試不同功能：

```bash
# 啟動開發伺服器
npm run dev

# 類型檢查
npm run type-check

# 建構專案
npm run build

# 測試 Supabase 連接（如果需要）
node test-supabase-connection.js
```

## 📊 資料庫架構概覽

執行 `schema.sql` 後，將創建以下資料表：

- `users` - 用戶基本資料
- `playlists` - 播放清單
- `channels` - 頻道資料
- `favorite_channels` - 收藏頻道
- `user_settings` - 用戶設定

## 🔐 安全性說明

- ✅ 已實作 Row Level Security (RLS)
- ✅ 用戶只能存取自己的資料
- ✅ 前端使用 anon key（安全）
- ⚠️ Service role key 僅供管理用途

## 📞 需要協助？

如果遇到問題，請檢查：

1. 瀏覽器開發者工具的 Console
2. Network 標籤中的 API 請求
3. Supabase Dashboard 的 Logs 部分

## 🎉 完成後的功能

設置完成後，您將擁有：

- 🔐 完整的用戶認證系統
- ☁️ 播放清單雲端同步
- ❤️ 收藏頻道跨裝置同步
- ⚙️ 用戶設定雲端儲存
- 📱 離線/在線狀態檢測
- 🔄 自動資料同步
