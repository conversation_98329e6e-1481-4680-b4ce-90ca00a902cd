import { createClient } from '@supabase/supabase-js';

// Supabase 配置
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('缺少 Supabase 環境變數。請檢查 .env 檔案中的 VITE_SUPABASE_URL 和 VITE_SUPABASE_ANON_KEY');
}

if (supabaseAnonKey === 'YOUR_REAL_ANON_KEY_HERE') {
  throw new Error('請更新 .env 檔案中的 VITE_SUPABASE_ANON_KEY 為真實的 Supabase anon key');
}

// 創建 Supabase 客戶端
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  },
  realtime: {
    params: {
      eventsPerSecond: 10
    }
  }
});

// 導出類型以供 TypeScript 使用
export type Database = {
  public: {
    Tables: {
      users: {
        Row: {
          id: string;
          email: string;
          username: string;
          avatar_url?: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          email: string;
          username: string;
          avatar_url?: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          email?: string;
          username?: string;
          avatar_url?: string;
          created_at?: string;
          updated_at?: string;
        };
      };
      playlists: {
        Row: {
          id: string;
          user_id: string;
          name: string;
          url: string;
          description?: string;
          is_public: boolean;
          total_channels: number;
          tvg_url?: string;
          catchup?: string;
          catchup_source?: string;
          user_agent?: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          name: string;
          url: string;
          description?: string;
          is_public?: boolean;
          total_channels?: number;
          tvg_url?: string;
          catchup?: string;
          catchup_source?: string;
          user_agent?: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          name?: string;
          url?: string;
          description?: string;
          is_public?: boolean;
          total_channels?: number;
          tvg_url?: string;
          catchup?: string;
          catchup_source?: string;
          user_agent?: string;
          created_at?: string;
          updated_at?: string;
        };
      };
      channels: {
        Row: {
          id: string;
          playlist_id: string;
          name: string;
          url: string;
          logo?: string;
          group_title?: string;
          tvg_id?: string;
          tvg_name?: string;
          resolution?: string;
          language?: string;
          catchup?: string;
          catchup_source?: string;
          duration?: number;
          user_agent?: string;
          referer?: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          playlist_id: string;
          name: string;
          url: string;
          logo?: string;
          group_title?: string;
          tvg_id?: string;
          tvg_name?: string;
          resolution?: string;
          language?: string;
          catchup?: string;
          catchup_source?: string;
          duration?: number;
          user_agent?: string;
          referer?: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          playlist_id?: string;
          name?: string;
          url?: string;
          logo?: string;
          group_title?: string;
          tvg_id?: string;
          tvg_name?: string;
          resolution?: string;
          language?: string;
          catchup?: string;
          catchup_source?: string;
          duration?: number;
          user_agent?: string;
          referer?: string;
          created_at?: string;
          updated_at?: string;
        };
      };
      favorite_channels: {
        Row: {
          id: string;
          user_id: string;
          channel_id: string;
          created_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          channel_id: string;
          created_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          channel_id?: string;
          created_at?: string;
        };
      };
      user_settings: {
        Row: {
          id: string;
          user_id: string;
          theme: string;
          autoplay: boolean;
          volume: number;
          quality: string;
          language: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          theme?: string;
          autoplay?: boolean;
          volume?: number;
          quality?: string;
          language?: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          theme?: string;
          autoplay?: boolean;
          volume?: number;
          quality?: string;
          language?: string;
          created_at?: string;
          updated_at?: string;
        };
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      [_ in never]: never;
    };
  };
};
