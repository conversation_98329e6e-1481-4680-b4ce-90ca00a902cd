import { create } from 'zustand';
import type { M3UPlaylist, M3UChannel, AppSettings, SyncStatus } from '../types';
import { StorageService } from '../services/storage';
import { DatabaseService } from '../services/database';
import { M3UParser } from '../services/m3uParser';
import { useAuthStore } from './authStore';

interface AppStore {
  // 狀態
  currentPlaylist: M3UPlaylist | null;
  recentPlaylists: M3UPlaylist[];
  favoriteChannels: M3UChannel[];
  settings: AppSettings;
  isLoading: boolean;
  error: string | null;
  syncStatus: SyncStatus;

  // 播放清單相關動作
  loadPlaylistFromUrl: (url: string) => Promise<void>;
  setCurrentPlaylist: (playlist: M3UPlaylist | null) => void;
  removeRecentPlaylist: (playlistId: string) => void;
  savePlaylistToCloud: (playlist: M3UPlaylist) => Promise<void>;
  loadUserPlaylists: () => Promise<void>;

  // 收藏頻道相關動作
  addFavoriteChannel: (channel: M3UChannel) => void;
  removeFavoriteChannel: (channelId: string) => void;
  isFavoriteChannel: (channelId: string) => boolean;
  syncFavoriteChannels: () => Promise<void>;

  // 設定相關動作
  updateSettings: (settings: Partial<AppSettings>) => void;
  syncUserSettings: () => Promise<void>;

  // 同步相關動作
  syncAllData: () => Promise<void>;
  setSyncStatus: (status: Partial<SyncStatus>) => void;

  // 通用動作
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
  initializeApp: () => void;
}

export const useAppStore = create<AppStore>((set, get) => ({
  // 初始狀態
  currentPlaylist: null,
  recentPlaylists: [],
  favoriteChannels: [],
  settings: StorageService.getSettings(),
  isLoading: false,
  error: null,
  syncStatus: {
    isOnline: navigator.onLine,
    lastSyncTime: null,
    pendingChanges: 0,
    isSyncing: false
  },

  // 播放清單相關動作
  loadPlaylistFromUrl: async (url: string) => {
    set({ isLoading: true, error: null });

    try {
      let result;

      // 檢查是否為 GitHub URL，使用專門的解析方法
      if (url.includes('github.com')) {
        result = await M3UParser.parseFromGitHubUrl(url);
      } else {
        result = await M3UParser.parseFromUrl(url);
      }

      if (result.success && result.data) {
        const playlist = result.data;

        // 儲存到最近使用
        StorageService.saveRecentPlaylist(playlist);

        // 更新狀態
        set({
          currentPlaylist: playlist,
          recentPlaylists: StorageService.getRecentPlaylists(),
          isLoading: false,
          error: null
        });
      } else {
        set({
          isLoading: false,
          error: result.error || '載入播放清單失敗'
        });
      }
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : '載入播放清單時發生未知錯誤'
      });
    }
  },

  setCurrentPlaylist: (playlist) => {
    set({ currentPlaylist: playlist });
  },

  removeRecentPlaylist: (playlistId) => {
    StorageService.removeRecentPlaylist(playlistId);
    set({ recentPlaylists: StorageService.getRecentPlaylists() });
  },

  savePlaylistToCloud: async (playlist: M3UPlaylist) => {
    const { user } = useAuthStore.getState();
    if (!user) return;

    set({ isLoading: true, error: null });

    try {
      const result = await DatabaseService.savePlaylist(playlist, user.id);
      if (result.success) {
        // 重新載入用戶播放清單
        await get().loadUserPlaylists();
        set({ isLoading: false });
      } else {
        set({
          isLoading: false,
          error: result.error || '儲存播放清單到雲端失敗'
        });
      }
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : '儲存播放清單時發生未知錯誤'
      });
    }
  },

  loadUserPlaylists: async () => {
    const { user } = useAuthStore.getState();
    if (!user) return;

    set({ isLoading: true, error: null });

    try {
      const result = await DatabaseService.getUserPlaylists(user.id);
      if (result.success && result.data) {
        // 將資料庫播放清單轉換為 M3UPlaylist 格式
        const playlists: M3UPlaylist[] = result.data.map(dbPlaylist => ({
          id: dbPlaylist.id,
          name: dbPlaylist.name,
          url: dbPlaylist.url,
          channels: [], // 需要時再載入頻道
          lastUpdated: new Date(dbPlaylist.updatedAt),
          totalChannels: dbPlaylist.totalChannels,
          tvgUrl: dbPlaylist.tvgUrl,
          catchup: dbPlaylist.catchup,
          catchupSource: dbPlaylist.catchupSource,
          userAgent: dbPlaylist.userAgent
        }));

        set({
          recentPlaylists: playlists,
          isLoading: false
        });
      } else {
        set({
          isLoading: false,
          error: result.error || '載入用戶播放清單失敗'
        });
      }
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : '載入用戶播放清單時發生未知錯誤'
      });
    }
  },

  // 收藏頻道相關動作
  addFavoriteChannel: async (channel) => {
    const { user } = useAuthStore.getState();

    // 先更新本地儲存
    StorageService.addFavoriteChannel(channel);
    set({ favoriteChannels: StorageService.getFavoriteChannels() });

    // 如果用戶已登入，同步到雲端
    if (user) {
      try {
        await DatabaseService.addFavoriteChannel(user.id, channel.id);
      } catch (error) {
        console.error('同步收藏頻道到雲端失敗:', error);
      }
    }
  },

  removeFavoriteChannel: async (channelId) => {
    const { user } = useAuthStore.getState();

    // 先更新本地儲存
    StorageService.removeFavoriteChannel(channelId);
    set({ favoriteChannels: StorageService.getFavoriteChannels() });

    // 如果用戶已登入，同步到雲端
    if (user) {
      try {
        await DatabaseService.removeFavoriteChannel(user.id, channelId);
      } catch (error) {
        console.error('從雲端移除收藏頻道失敗:', error);
      }
    }
  },

  isFavoriteChannel: (channelId) => {
    const { favoriteChannels } = get();
    return favoriteChannels.some(channel => channel.id === channelId);
  },

  syncFavoriteChannels: async () => {
    const { user } = useAuthStore.getState();
    if (!user) return;

    try {
      const result = await DatabaseService.getUserFavoriteChannels(user.id);
      if (result.success && result.data) {
        // 將資料庫頻道轉換為 M3UChannel 格式
        const channels: M3UChannel[] = result.data.map(dbChannel => ({
          id: dbChannel.id,
          name: dbChannel.name,
          url: dbChannel.url,
          logo: dbChannel.logo,
          group: dbChannel.groupTitle,
          tvgId: dbChannel.tvgId,
          tvgName: dbChannel.tvgName,
          resolution: dbChannel.resolution,
          language: dbChannel.language,
          catchup: dbChannel.catchup,
          catchupSource: dbChannel.catchupSource,
          duration: dbChannel.duration,
          userAgent: dbChannel.userAgent,
          referer: dbChannel.referer
        }));

        set({ favoriteChannels: channels });

        // 同步到本地儲存
        channels.forEach(channel => {
          StorageService.addFavoriteChannel(channel);
        });
      }
    } catch (error) {
      console.error('同步收藏頻道失敗:', error);
    }
  },

  // 設定相關動作
  updateSettings: async (newSettings) => {
    const { user } = useAuthStore.getState();
    const currentSettings = get().settings;
    const updatedSettings = { ...currentSettings, ...newSettings };

    // 先更新本地儲存
    StorageService.saveSettings(updatedSettings);
    set({ settings: updatedSettings });

    // 如果用戶已登入，同步到雲端
    if (user) {
      try {
        await DatabaseService.updateUserSettings(user.id, updatedSettings);
      } catch (error) {
        console.error('同步設定到雲端失敗:', error);
      }
    }
  },

  syncUserSettings: async () => {
    const { user } = useAuthStore.getState();
    if (!user) return;

    try {
      const result = await DatabaseService.getUserSettings(user.id);
      if (result.success && result.data) {
        const settings: AppSettings = {
          theme: result.data.theme,
          autoplay: result.data.autoplay,
          volume: result.data.volume,
          quality: result.data.quality,
          language: result.data.language
        };

        set({ settings });
        StorageService.saveSettings(settings);
      }
    } catch (error) {
      console.error('同步用戶設定失敗:', error);
    }
  },

  // 同步相關動作
  syncAllData: async () => {
    const { user } = useAuthStore.getState();
    if (!user) return;

    set({
      syncStatus: {
        ...get().syncStatus,
        isSyncing: true
      }
    });

    try {
      // 同步播放清單
      await get().loadUserPlaylists();

      // 同步收藏頻道
      await get().syncFavoriteChannels();

      // 同步用戶設定
      await get().syncUserSettings();

      set({
        syncStatus: {
          ...get().syncStatus,
          isSyncing: false,
          lastSyncTime: new Date(),
          pendingChanges: 0
        }
      });
    } catch (error) {
      set({
        syncStatus: {
          ...get().syncStatus,
          isSyncing: false
        },
        error: error instanceof Error ? error.message : '同步資料時發生錯誤'
      });
    }
  },

  setSyncStatus: (status) => {
    set({
      syncStatus: {
        ...get().syncStatus,
        ...status
      }
    });
  },

  // 通用動作
  setLoading: (loading) => {
    set({ isLoading: loading });
  },

  setError: (error) => {
    set({ error });
  },

  clearError: () => {
    set({ error: null });
  },

  initializeApp: async () => {
    // 從本地儲存載入資料
    const recentPlaylists = StorageService.getRecentPlaylists();
    const favoriteChannels = StorageService.getFavoriteChannels();
    const settings = StorageService.getSettings();

    set({
      recentPlaylists,
      favoriteChannels,
      settings
    });

    // 檢查網路狀態
    const updateOnlineStatus = () => {
      set({
        syncStatus: {
          ...get().syncStatus,
          isOnline: navigator.onLine
        }
      });
    };

    window.addEventListener('online', updateOnlineStatus);
    window.addEventListener('offline', updateOnlineStatus);

    // 如果用戶已登入且有網路連接，嘗試同步資料
    const { user } = useAuthStore.getState();
    if (user && navigator.onLine) {
      await get().syncAllData();
    }
  }
}));
