import { create } from 'zustand';
import { supabase } from '../config/supabase';
import { DatabaseService } from '../services/database';
import type { User, AuthState } from '../types';

interface AuthStore extends AuthState {
  // 認證相關動作
  signUp: (email: string, password: string, username: string) => Promise<boolean>;
  signIn: (email: string, password: string) => Promise<boolean>;
  signOut: () => Promise<void>;
  checkAuth: () => Promise<void>;
  clearError: () => void;
  
  // 用戶資料相關動作
  updateUserProfile: (updates: Partial<User>) => Promise<boolean>;
}

export const useAuthStore = create<AuthStore>((set, get) => ({
  // 初始狀態
  user: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,

  // 註冊
  signUp: async (email: string, password: string, username: string) => {
    set({ isLoading: true, error: null });

    try {
      const result = await DatabaseService.signUp(email, password, username);
      
      if (result.success && result.data) {
        set({
          user: result.data,
          isAuthenticated: true,
          isLoading: false,
          error: null
        });
        return true;
      } else {
        set({
          isLoading: false,
          error: result.error || '註冊失敗'
        });
        return false;
      }
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : '註冊時發生未知錯誤'
      });
      return false;
    }
  },

  // 登入
  signIn: async (email: string, password: string) => {
    set({ isLoading: true, error: null });

    try {
      const result = await DatabaseService.signIn(email, password);
      
      if (result.success && result.data) {
        set({
          user: result.data,
          isAuthenticated: true,
          isLoading: false,
          error: null
        });
        return true;
      } else {
        set({
          isLoading: false,
          error: result.error || '登入失敗'
        });
        return false;
      }
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : '登入時發生未知錯誤'
      });
      return false;
    }
  },

  // 登出
  signOut: async () => {
    set({ isLoading: true });

    try {
      await DatabaseService.signOut();
      set({
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: null
      });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : '登出時發生錯誤'
      });
    }
  },

  // 檢查認證狀態
  checkAuth: async () => {
    set({ isLoading: true });

    try {
      const result = await DatabaseService.getCurrentUser();
      
      if (result.success && result.data) {
        set({
          user: result.data,
          isAuthenticated: true,
          isLoading: false,
          error: null
        });
      } else {
        set({
          user: null,
          isAuthenticated: false,
          isLoading: false,
          error: null
        });
      }
    } catch (error) {
      set({
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: error instanceof Error ? error.message : '檢查認證狀態時發生錯誤'
      });
    }
  },

  // 清除錯誤
  clearError: () => {
    set({ error: null });
  },

  // 更新用戶資料
  updateUserProfile: async (updates: Partial<User>) => {
    const { user } = get();
    if (!user) return false;

    set({ isLoading: true, error: null });

    try {
      // 這裡可以實作更新用戶資料的邏輯
      // 目前先更新本地狀態
      const updatedUser = { ...user, ...updates };
      
      set({
        user: updatedUser,
        isLoading: false,
        error: null
      });
      
      return true;
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : '更新用戶資料時發生錯誤'
      });
      return false;
    }
  }
}));

// 監聽認證狀態變化
supabase.auth.onAuthStateChange(async (event, session) => {
  const { checkAuth } = useAuthStore.getState();
  
  if (event === 'SIGNED_IN' && session) {
    await checkAuth();
  } else if (event === 'SIGNED_OUT') {
    useAuthStore.setState({
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: null
    });
  }
});
