import { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { useAppStore } from './stores/appStore';
import { useAuthStore } from './stores/authStore';
import { Home } from './pages/Home/Home';
import { ChannelListPage } from './pages/ChannelList/ChannelListPage';
import { PlayerPage } from './pages/Player/PlayerPage';
import { SettingsPage } from './pages/Settings/SettingsPage';
import { M3UTestPage } from './pages/M3UTest';
import { DiagnosticPage } from './pages/Diagnostic/DiagnosticPage';
import { LoginPage } from './pages/Login';
import { RegisterPage } from './pages/Register';
import { Navigation } from './components/Navigation/Navigation';

function App() {
  const { initializeApp } = useAppStore();
  const { checkAuth } = useAuthStore();

  useEffect(() => {
    // 檢查認證狀態
    checkAuth();
    // 初始化應用程式
    initializeApp();
  }, [initializeApp, checkAuth]);

  return (
    <Router>
      <div className="min-h-screen bg-gray-50">
        <Navigation />
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/channels" element={<ChannelListPage />} />
          <Route path="/player" element={<PlayerPage />} />
          <Route path="/settings" element={<SettingsPage />} />
          <Route path="/test" element={<M3UTestPage />} />
          <Route path="/diagnostic" element={<DiagnosticPage />} />
          <Route path="/login" element={<LoginPage />} />
          <Route path="/register" element={<RegisterPage />} />
          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>
      </div>
    </Router>
  );
}

export default App;
