import { supabase } from '../config/supabase';
import type {
  User,
  DatabasePlaylist,
  DatabaseChannel,
  FavoriteChannel,
  UserSettings,
  M3UPlaylist,
  ApiResponse
} from '../types';

export class DatabaseService {
  // 用戶認證相關方法
  static async signUp(email: string, password: string, username: string): Promise<ApiResponse<User>> {
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            username
          }
        }
      });

      if (error) {
        return { success: false, error: error.message };
      }

      if (data.user) {
        // 創建用戶資料
        const { error: profileError } = await supabase
          .from('users')
          .insert({
            id: data.user.id,
            email: data.user.email!,
            username
          });

        if (profileError) {
          return { success: false, error: profileError.message };
        }

        // 創建預設設定
        await this.createDefaultUserSettings(data.user.id);

        const user: User = {
          id: data.user.id,
          email: data.user.email!,
          username,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };

        return { success: true, data: user };
      }

      return { success: false, error: '註冊失敗' };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : '註冊時發生未知錯誤' 
      };
    }
  }

  static async signIn(email: string, password: string): Promise<ApiResponse<User>> {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      });

      if (error) {
        return { success: false, error: error.message };
      }

      if (data.user) {
        const user = await this.getUserProfile(data.user.id);
        if (user.success && user.data) {
          return { success: true, data: user.data };
        }
      }

      return { success: false, error: '登入失敗' };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : '登入時發生未知錯誤' 
      };
    }
  }

  static async signOut(): Promise<ApiResponse<void>> {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) {
        return { success: false, error: error.message };
      }
      return { success: true };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : '登出時發生未知錯誤' 
      };
    }
  }

  static async getCurrentUser(): Promise<ApiResponse<User>> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        return { success: false, error: '未找到用戶' };
      }

      return await this.getUserProfile(user.id);
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : '獲取用戶資料時發生錯誤' 
      };
    }
  }

  static async getUserProfile(userId: string): Promise<ApiResponse<User>> {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) {
        return { success: false, error: error.message };
      }

      const user: User = {
        id: data.id,
        email: data.email,
        username: data.username,
        avatarUrl: data.avatar_url,
        createdAt: data.created_at,
        updatedAt: data.updated_at
      };

      return { success: true, data: user };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : '獲取用戶資料時發生錯誤' 
      };
    }
  }

  // 播放清單相關方法
  static async savePlaylist(playlist: M3UPlaylist, userId: string): Promise<ApiResponse<DatabasePlaylist>> {
    try {
      const { data, error } = await supabase
        .from('playlists')
        .insert({
          user_id: userId,
          name: playlist.name,
          url: playlist.url,
          total_channels: playlist.totalChannels,
          tvg_url: playlist.tvgUrl,
          catchup: playlist.catchup,
          catchup_source: playlist.catchupSource,
          user_agent: playlist.userAgent
        })
        .select()
        .single();

      if (error) {
        return { success: false, error: error.message };
      }

      // 儲存頻道資料
      if (playlist.channels.length > 0) {
        const channelsData = playlist.channels.map(channel => ({
          playlist_id: data.id,
          name: channel.name,
          url: channel.url,
          logo: channel.logo,
          group_title: channel.group,
          tvg_id: channel.tvgId,
          tvg_name: channel.tvgName,
          resolution: channel.resolution,
          language: channel.language,
          catchup: channel.catchup,
          catchup_source: channel.catchupSource,
          duration: channel.duration,
          user_agent: channel.userAgent,
          referer: channel.referer
        }));

        const { error: channelsError } = await supabase
          .from('channels')
          .insert(channelsData);

        if (channelsError) {
          // 如果頻道儲存失敗，刪除已創建的播放清單
          await supabase.from('playlists').delete().eq('id', data.id);
          return { success: false, error: channelsError.message };
        }
      }

      const dbPlaylist: DatabasePlaylist = {
        id: data.id,
        userId: data.user_id,
        name: data.name,
        url: data.url,
        description: data.description,
        isPublic: data.is_public,
        totalChannels: data.total_channels,
        tvgUrl: data.tvg_url,
        catchup: data.catchup,
        catchupSource: data.catchup_source,
        userAgent: data.user_agent,
        createdAt: data.created_at,
        updatedAt: data.updated_at
      };

      return { success: true, data: dbPlaylist };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : '儲存播放清單時發生錯誤' 
      };
    }
  }

  static async getUserPlaylists(userId: string): Promise<ApiResponse<DatabasePlaylist[]>> {
    try {
      const { data, error } = await supabase
        .from('playlists')
        .select('*')
        .eq('user_id', userId)
        .order('updated_at', { ascending: false });

      if (error) {
        return { success: false, error: error.message };
      }

      const playlists: DatabasePlaylist[] = data.map(item => ({
        id: item.id,
        userId: item.user_id,
        name: item.name,
        url: item.url,
        description: item.description,
        isPublic: item.is_public,
        totalChannels: item.total_channels,
        tvgUrl: item.tvg_url,
        catchup: item.catchup,
        catchupSource: item.catchup_source,
        userAgent: item.user_agent,
        createdAt: item.created_at,
        updatedAt: item.updated_at
      }));

      return { success: true, data: playlists };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : '獲取播放清單時發生錯誤' 
      };
    }
  }

  static async getPlaylistChannels(playlistId: string): Promise<ApiResponse<DatabaseChannel[]>> {
    try {
      const { data, error } = await supabase
        .from('channels')
        .select('*')
        .eq('playlist_id', playlistId)
        .order('name');

      if (error) {
        return { success: false, error: error.message };
      }

      const channels: DatabaseChannel[] = data.map(item => ({
        id: item.id,
        playlistId: item.playlist_id,
        name: item.name,
        url: item.url,
        logo: item.logo,
        groupTitle: item.group_title,
        tvgId: item.tvg_id,
        tvgName: item.tvg_name,
        resolution: item.resolution,
        language: item.language,
        catchup: item.catchup,
        catchupSource: item.catchup_source,
        duration: item.duration,
        userAgent: item.user_agent,
        referer: item.referer,
        createdAt: item.created_at,
        updatedAt: item.updated_at
      }));

      return { success: true, data: channels };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : '獲取頻道列表時發生錯誤' 
      };
    }
  }

  // 收藏頻道相關方法
  static async addFavoriteChannel(userId: string, channelId: string): Promise<ApiResponse<FavoriteChannel>> {
    try {
      const { data, error } = await supabase
        .from('favorite_channels')
        .insert({
          user_id: userId,
          channel_id: channelId
        })
        .select()
        .single();

      if (error) {
        return { success: false, error: error.message };
      }

      const favorite: FavoriteChannel = {
        id: data.id,
        userId: data.user_id,
        channelId: data.channel_id,
        createdAt: data.created_at
      };

      return { success: true, data: favorite };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : '新增收藏頻道時發生錯誤' 
      };
    }
  }

  static async removeFavoriteChannel(userId: string, channelId: string): Promise<ApiResponse<void>> {
    try {
      const { error } = await supabase
        .from('favorite_channels')
        .delete()
        .eq('user_id', userId)
        .eq('channel_id', channelId);

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : '移除收藏頻道時發生錯誤' 
      };
    }
  }

  static async getUserFavoriteChannels(userId: string): Promise<ApiResponse<DatabaseChannel[]>> {
    try {
      const { data, error } = await supabase
        .from('favorite_channels')
        .select(`
          channel_id,
          channels (*)
        `)
        .eq('user_id', userId);

      if (error) {
        return { success: false, error: error.message };
      }

      const channels: DatabaseChannel[] = data
        .filter(item => item.channels)
        .map(item => {
          const channel = item.channels as any;
          return {
            id: channel.id,
            playlistId: channel.playlist_id,
            name: channel.name,
            url: channel.url,
            logo: channel.logo,
            groupTitle: channel.group_title,
            tvgId: channel.tvg_id,
            tvgName: channel.tvg_name,
            resolution: channel.resolution,
            language: channel.language,
            catchup: channel.catchup,
            catchupSource: channel.catchup_source,
            duration: channel.duration,
            userAgent: channel.user_agent,
            referer: channel.referer,
            createdAt: channel.created_at,
            updatedAt: channel.updated_at
          };
        });

      return { success: true, data: channels };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : '獲取收藏頻道時發生錯誤' 
      };
    }
  }

  // 用戶設定相關方法
  static async createDefaultUserSettings(userId: string): Promise<ApiResponse<UserSettings>> {
    try {
      const { data, error } = await supabase
        .from('user_settings')
        .insert({
          user_id: userId,
          theme: 'auto',
          autoplay: false,
          volume: 80,
          quality: 'auto',
          language: 'zh-TW'
        })
        .select()
        .single();

      if (error) {
        return { success: false, error: error.message };
      }

      const settings: UserSettings = {
        id: data.id,
        userId: data.user_id,
        theme: data.theme,
        autoplay: data.autoplay,
        volume: data.volume,
        quality: data.quality,
        language: data.language,
        createdAt: data.created_at,
        updatedAt: data.updated_at
      };

      return { success: true, data: settings };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : '創建用戶設定時發生錯誤' 
      };
    }
  }

  static async getUserSettings(userId: string): Promise<ApiResponse<UserSettings>> {
    try {
      const { data, error } = await supabase
        .from('user_settings')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error) {
        return { success: false, error: error.message };
      }

      const settings: UserSettings = {
        id: data.id,
        userId: data.user_id,
        theme: data.theme,
        autoplay: data.autoplay,
        volume: data.volume,
        quality: data.quality,
        language: data.language,
        createdAt: data.created_at,
        updatedAt: data.updated_at
      };

      return { success: true, data: settings };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : '獲取用戶設定時發生錯誤' 
      };
    }
  }

  static async updateUserSettings(userId: string, settings: Partial<UserSettings>): Promise<ApiResponse<UserSettings>> {
    try {
      const { data, error } = await supabase
        .from('user_settings')
        .update({
          theme: settings.theme,
          autoplay: settings.autoplay,
          volume: settings.volume,
          quality: settings.quality,
          language: settings.language,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId)
        .select()
        .single();

      if (error) {
        return { success: false, error: error.message };
      }

      const updatedSettings: UserSettings = {
        id: data.id,
        userId: data.user_id,
        theme: data.theme,
        autoplay: data.autoplay,
        volume: data.volume,
        quality: data.quality,
        language: data.language,
        createdAt: data.created_at,
        updatedAt: data.updated_at
      };

      return { success: true, data: updatedSettings };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : '更新用戶設定時發生錯誤' 
      };
    }
  }
}
