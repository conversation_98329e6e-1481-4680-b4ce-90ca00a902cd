// CORS 代理工具
export class CorsProxy {
  private static proxies = [
    // 移動端優化的代理服務器（按穩定性排序）
    'https://api.allorigins.win/raw?url=',
    'https://corsproxy.io/?',
    'https://api.cors.lol/?url=',
    'https://proxy.cors.sh/',
    'https://thingproxy.freeboard.io/fetch/',
    'https://api.codetabs.com/v1/proxy?quest=',
    // 備用代理（可能不穩定）
    'https://cors-anywhere.herokuapp.com/',
    // 新增移動端友好的代理
    'https://cors-proxy.htmldriven.com/?url=',
    'https://yacdn.org/proxy/',
  ];

  private static currentProxyIndex = 0;

  /**
   * 獲取當前代理 URL
   */
  private static getCurrentProxy(): string {
    return this.proxies[this.currentProxyIndex];
  }

  /**
   * 切換到下一個代理
   */
  private static switchToNextProxy(): void {
    this.currentProxyIndex = (this.currentProxyIndex + 1) % this.proxies.length;
    console.log(`🔄 切換到代理: ${this.getCurrentProxy()}`);
  }

  /**
   * 使用代理獲取資源
   */
  static async fetchWithProxy(url: string, options: RequestInit = {}): Promise<Response> {
    const maxRetries = this.proxies.length;
    let lastError: Error | null = null;

    for (let attempt = 0; attempt < maxRetries; attempt++) {
      try {
        const proxy = this.getCurrentProxy();
        const proxyUrl = `${proxy}${encodeURIComponent(url)}`;
        
        console.log(`🌐 嘗試使用代理 ${attempt + 1}/${maxRetries}: ${proxy}`);
        console.log(`📡 請求 URL: ${proxyUrl}`);

        const response = await fetch(proxyUrl, {
          ...options,
          headers: {
            'Accept': 'text/plain, application/x-mpegURL, */*',
            'User-Agent': 'TVBOX/1.0',
            ...options.headers,
          },
        });

        if (response.ok) {
          console.log(`✅ 代理請求成功: ${proxy}`);
          return response;
        } else {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
      } catch (error) {
        console.warn(`❌ 代理失敗 ${attempt + 1}/${maxRetries}:`, error);
        lastError = error as Error;
        
        if (attempt < maxRetries - 1) {
          this.switchToNextProxy();
          // 等待一下再重試
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }
    }

    // 所有代理都失敗，嘗試直接請求
    console.log('🔄 所有代理都失敗，嘗試直接請求...');
    try {
      const response = await fetch(url, options);
      if (response.ok) {
        console.log('✅ 直接請求成功');
        return response;
      }
    } catch (error) {
      console.warn('❌ 直接請求也失敗:', error);
    }

    throw new Error(`所有請求方式都失敗。最後錯誤: ${lastError?.message}`);
  }

  /**
   * 檢查 URL 是否需要代理
   */
  static needsProxy(url: string): boolean {
    try {
      const urlObj = new URL(url);
      const currentOrigin = window.location.origin;

      // 如果是同源請求，不需要代理
      if (urlObj.origin === currentOrigin) {
        return false;
      }

      // 檢查是否是已知的需要代理的域名
      const needsProxyDomains = [
        'files.catbox.moe',
        'raw.githubusercontent.com',
        'gist.githubusercontent.com',
        'pastebin.com',
        // 常見的 IPTV 串流域名
        'breezy-audrie',
        'koyeb.app',
        'zspace',
        'smart.pendy.dpdns.org', // 從您的截圖中看到的域名
        'pendy.dpdns.org',
        'dpdns.org',
        // 其他可能需要代理的域名
        'cdn.jsdelivr.net',
        'gitee.com',
        'gitlab.com',
        // 移動端常見問題域名
        'herokuapp.com',
        'netlify.app',
        'vercel.app',
        'github.io',
      ];

      return needsProxyDomains.some(domain => urlObj.hostname.includes(domain));
    } catch {
      return false;
    }
  }

  /**
   * 智能獲取資源（自動判斷是否需要代理）
   */
  static async smartFetch(url: string, options: RequestInit = {}): Promise<Response> {
    console.log(`🔍 智能請求: ${url}`);

    if (this.needsProxy(url)) {
      console.log('🌐 檢測到需要代理的 URL，使用代理請求');
      return this.fetchWithProxy(url, options);
    } else {
      console.log('📡 直接請求');
      return fetch(url, options);
    }
  }

  /**
   * 為 HLS 串流生成代理 URL
   */
  static getProxyUrl(originalUrl: string): string {
    // 檢查是否需要代理
    if (!this.needsProxy(originalUrl)) {
      return originalUrl;
    }

    const proxy = this.getCurrentProxy();
    const proxyUrl = `${proxy}${encodeURIComponent(originalUrl)}`;
    console.log(`🔄 生成代理 URL: ${originalUrl} -> ${proxyUrl}`);
    return proxyUrl;
  }

  /**
   * 嘗試多個代理 URL 直到找到可用的
   */
  static async findWorkingProxyUrl(originalUrl: string): Promise<string> {
    // 如果不需要代理，直接返回原始 URL
    if (!this.needsProxy(originalUrl)) {
      return originalUrl;
    }

    console.log(`🔍 尋找可用的代理 URL: ${originalUrl}`);

    for (let i = 0; i < this.proxies.length; i++) {
      try {
        const proxy = this.proxies[i];
        const proxyUrl = `${proxy}${encodeURIComponent(originalUrl)}`;

        console.log(`🧪 測試代理 ${i + 1}/${this.proxies.length}: ${proxy}`);

        // 測試代理是否可用（使用 HEAD 請求）
        const response = await fetch(proxyUrl, {
          method: 'HEAD',
          signal: AbortSignal.timeout(5000) // 5秒超時
        });

        if (response.ok) {
          console.log(`✅ 找到可用代理: ${proxy}`);
          this.currentProxyIndex = i; // 更新當前代理索引
          return proxyUrl;
        }
      } catch (error) {
        console.warn(`❌ 代理測試失敗:`, error);
      }
    }

    console.warn('⚠️ 所有代理都不可用，返回原始 URL');
    return originalUrl;
  }
}

// 導出便捷函數
export const fetchWithCorsProxy = CorsProxy.fetchWithProxy.bind(CorsProxy);
export const smartFetch = CorsProxy.smartFetch.bind(CorsProxy);
