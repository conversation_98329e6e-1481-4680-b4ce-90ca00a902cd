// 移動端優化工具
export class MobileOptimization {
  /**
   * 檢測是否為移動設備
   */
  static isMobile(): boolean {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
  }

  /**
   * 檢測是否為 iOS 設備
   */
  static isIOS(): boolean {
    return /iPad|iPhone|iPod/.test(navigator.userAgent);
  }

  /**
   * 檢測是否為 Android 設備
   */
  static isAndroid(): boolean {
    return /Android/.test(navigator.userAgent);
  }

  /**
   * 檢測是否為 Safari 瀏覽器
   */
  static isSafari(): boolean {
    return /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
  }

  /**
   * 檢測是否為微信內置瀏覽器
   */
  static isWeChat(): boolean {
    return /MicroMessenger/i.test(navigator.userAgent);
  }

  /**
   * 檢測是否為 Chrome 移動版
   */
  static isChromeMobile(): boolean {
    return /Chrome/.test(navigator.userAgent) && this.isMobile();
  }

  /**
   * 獲取移動端優化的 HLS 配置
   */
  static getMobileHLSConfig() {
    const baseConfig = {
      // 基本配置
      enableWorker: false, // 移動端禁用 Web Worker
      enableSoftwareAES: true, // 啟用軟體 AES 解密
      lowLatencyMode: false, // 移動端關閉低延遲模式以節省電量
      
      // 緩衝配置（移動端優化）
      maxBufferLength: 30, // 減少緩衝長度以節省記憶體
      maxMaxBufferLength: 60,
      maxBufferSize: 60 * 1000 * 1000, // 60MB
      maxBufferHole: 0.5,
      
      // 載入配置
      maxLoadingDelay: 4,
      maxRetry: 3, // 減少重試次數
      maxRetryDelay: 8,
      retryDelayOnError: 2,
      
      // 片段配置
      fragLoadingTimeOut: 20000, // 增加超時時間
      manifestLoadingTimeOut: 10000,
      levelLoadingTimeOut: 10000,
      
      // 移動端特殊配置
      startLevel: -1, // 自動選擇起始品質
      capLevelToPlayerSize: true, // 根據播放器大小限制品質
      testBandwidth: false, // 移動端關閉頻寬測試
      
      // CORS 和網路配置
      xhrSetup: (xhr: XMLHttpRequest, url: string) => {
        // 移動端 XHR 配置
        xhr.withCredentials = false;
        xhr.timeout = 30000; // 30秒超時
        
        // 設定移動端友好的標頭
        xhr.setRequestHeader('Accept', 'application/vnd.apple.mpegurl,application/x-mpegurl,video/mp4,*/*');
        xhr.setRequestHeader('Accept-Language', 'zh-TW,zh;q=0.9,en;q=0.8');
        xhr.setRequestHeader('Cache-Control', 'no-cache');
        
        // iOS Safari 特殊處理
        if (MobileOptimization.isIOS()) {
          xhr.setRequestHeader('User-Agent', 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1');
        }
        
        // Android Chrome 特殊處理
        if (MobileOptimization.isAndroid()) {
          xhr.setRequestHeader('User-Agent', 'Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36');
        }
        
        // 微信瀏覽器特殊處理
        if (MobileOptimization.isWeChat()) {
          xhr.setRequestHeader('User-Agent', 'Mozilla/5.0 (Linux; Android 11; SAMSUNG SM-G973U) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/14.2 Chrome/87.0.4280.141 Mobile Safari/537.36');
        }
      }
    };

    // iOS Safari 特殊配置
    if (this.isIOS() && this.isSafari()) {
      return {
        ...baseConfig,
        // iOS Safari 不支援 MSE，使用原生 HLS
        enableWorker: false,
        enableSoftwareAES: false,
        maxBufferLength: 20, // iOS 記憶體限制更嚴格
        maxMaxBufferLength: 40,
      };
    }

    // Android 特殊配置
    if (this.isAndroid()) {
      return {
        ...baseConfig,
        // Android 可以使用更多功能
        enableSoftwareAES: true,
        maxBufferLength: 40,
        maxMaxBufferLength: 80,
      };
    }

    return baseConfig;
  }

  /**
   * 獲取移動端優化的 video 元素屬性
   */
  static getMobileVideoAttributes() {
    const baseAttributes = {
      playsInline: true, // 防止全螢幕播放
      preload: 'metadata' as const, // 減少初始載入
      crossOrigin: 'anonymous' as const, // CORS 設定
      controls: true,
      muted: false, // 移動端通常需要用戶互動才能播放聲音
    };

    // iOS 特殊屬性
    if (this.isIOS()) {
      return {
        ...baseAttributes,
        'webkit-playsinline': 'true',
        'playsinline': 'true',
        // iOS 自動播放限制
        autoPlay: false,
      };
    }

    // Android 特殊屬性
    if (this.isAndroid()) {
      return {
        ...baseAttributes,
        // Android 特殊屬性
        'x5-video-player-type': 'h5',
        'x5-video-player-fullscreen': 'true',
        'x5-video-orientation': 'portraint',
        // 騰訊 X5 內核優化
        'x5-playsinline': 'true',
      };
    }

    return baseAttributes;
  }

  /**
   * 檢查是否支援 HLS
   */
  static supportsHLS(): boolean {
    const video = document.createElement('video');
    return video.canPlayType('application/vnd.apple.mpegurl') !== '' ||
           video.canPlayType('application/x-mpegurl') !== '';
  }

  /**
   * 檢查是否支援 MSE (Media Source Extensions)
   */
  static supportsMSE(): boolean {
    return 'MediaSource' in window && MediaSource.isTypeSupported('video/mp4; codecs="avc1.42E01E,mp4a.40.2"');
  }

  /**
   * 獲取最佳播放策略
   */
  static getBestPlaybackStrategy(): 'native' | 'hls.js' | 'direct' {
    // iOS Safari 優先使用原生 HLS
    if (this.isIOS() && this.isSafari()) {
      return 'native';
    }

    // 其他支援 MSE 的瀏覽器使用 HLS.js
    if (this.supportsMSE()) {
      return 'hls.js';
    }

    // 支援原生 HLS 的瀏覽器
    if (this.supportsHLS()) {
      return 'native';
    }

    // 最後嘗試直接播放
    return 'direct';
  }

  /**
   * 移動端錯誤處理
   */
  static handleMobileError(error: any): string {
    const errorMessage = error?.message || error?.toString() || '未知錯誤';
    
    // iOS 特殊錯誤處理
    if (this.isIOS()) {
      if (errorMessage.includes('decode')) {
        return 'iOS 解碼錯誤：請嘗試重新載入或使用其他播放清單';
      }
      if (errorMessage.includes('network')) {
        return 'iOS 網路錯誤：請檢查網路連接或嘗試使用代理';
      }
    }

    // Android 特殊錯誤處理
    if (this.isAndroid()) {
      if (errorMessage.includes('CORS')) {
        return 'Android CORS 錯誤：正在嘗試使用代理服務器...';
      }
      if (errorMessage.includes('format')) {
        return 'Android 格式錯誤：此串流格式可能不受支援';
      }
    }

    // 微信瀏覽器特殊錯誤處理
    if (this.isWeChat()) {
      return '微信瀏覽器限制：建議複製連結到外部瀏覽器播放';
    }

    // 通用 CORS 錯誤
    if (errorMessage.toLowerCase().includes('cors') || 
        errorMessage.includes('跨域') ||
        errorMessage.includes('Access to fetch')) {
      return 'CORS 跨域限制：系統正在嘗試使用代理服務器解決此問題';
    }

    return `移動端播放錯誤：${errorMessage}`;
  }

  /**
   * 獲取移動端診斷資訊
   */
  static getDiagnosticInfo() {
    return {
      isMobile: this.isMobile(),
      isIOS: this.isIOS(),
      isAndroid: this.isAndroid(),
      isSafari: this.isSafari(),
      isWeChat: this.isWeChat(),
      isChromeMobile: this.isChromeMobile(),
      supportsHLS: this.supportsHLS(),
      supportsMSE: this.supportsMSE(),
      bestStrategy: this.getBestPlaybackStrategy(),
      userAgent: navigator.userAgent,
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight,
        devicePixelRatio: window.devicePixelRatio || 1
      },
      connection: (navigator as any).connection ? {
        effectiveType: (navigator as any).connection.effectiveType,
        downlink: (navigator as any).connection.downlink,
        rtt: (navigator as any).connection.rtt
      } : null
    };
  }
}
