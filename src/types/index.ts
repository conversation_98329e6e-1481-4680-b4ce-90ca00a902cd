// M3U 播放清單相關類型
export interface M3UChannel {
  id: string;
  name: string;
  url: string;
  logo?: string;
  group?: string;
  tvgId?: string;
  tvgName?: string;
  resolution?: string;
  language?: string;
  // 新增的屬性
  catchup?: string;
  catchupSource?: string;
  duration?: number;
  userAgent?: string;
  referer?: string;
}

export interface M3UPlaylist {
  id: string;
  name: string;
  url: string;
  channels: M3UChannel[];
  lastUpdated: Date;
  totalChannels: number;
  // 新增的播放清單屬性
  tvgUrl?: string;
  catchup?: string;
  catchupSource?: string;
  userAgent?: string;
}

// 播放器相關類型
export interface PlayerState {
  isPlaying: boolean;
  currentChannel: M3UChannel | null;
  volume: number;
  isMuted: boolean;
  isFullscreen: boolean;
  currentTime: number;
  duration: number;
  isLoading: boolean;
  error: string | null;
}

// 應用狀態類型
export interface AppState {
  currentPlaylist: M3UPlaylist | null;
  recentPlaylists: M3UPlaylist[];
  favoriteChannels: M3UChannel[];
  settings: AppSettings;
  isLoading: boolean;
  error: string | null;
}

export interface AppSettings {
  theme: 'light' | 'dark' | 'auto';
  autoplay: boolean;
  volume: number;
  quality: 'auto' | 'high' | 'medium' | 'low';
  language: string;
}

// QR 掃描相關類型
export interface QRScanResult {
  text: string;
  timestamp: Date;
}

// 本地儲存相關類型
export interface StorageData {
  recentPlaylists: M3UPlaylist[];
  favoriteChannels: M3UChannel[];
  settings: AppSettings;
}

// API 相關類型
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

// 路由相關類型
export type RouteParams = {
  '/': {};
  '/channels': {};
  '/player': { channelId?: string };
  '/settings': {};
  '/login': {};
  '/register': {};
  '/profile': {};
};

// 用戶相關類型
export interface User {
  id: string;
  email: string;
  username: string;
  avatarUrl?: string;
  createdAt: string;
  updatedAt: string;
}

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

// 資料庫相關類型
export interface DatabasePlaylist {
  id: string;
  userId: string;
  name: string;
  url: string;
  description?: string;
  isPublic: boolean;
  totalChannels: number;
  tvgUrl?: string;
  catchup?: string;
  catchupSource?: string;
  userAgent?: string;
  createdAt: string;
  updatedAt: string;
}

export interface DatabaseChannel {
  id: string;
  playlistId: string;
  name: string;
  url: string;
  logo?: string;
  groupTitle?: string;
  tvgId?: string;
  tvgName?: string;
  resolution?: string;
  language?: string;
  catchup?: string;
  catchupSource?: string;
  duration?: number;
  userAgent?: string;
  referer?: string;
  createdAt: string;
  updatedAt: string;
}

export interface FavoriteChannel {
  id: string;
  userId: string;
  channelId: string;
  createdAt: string;
}

export interface UserSettings {
  id: string;
  userId: string;
  theme: 'light' | 'dark' | 'auto';
  autoplay: boolean;
  volume: number;
  quality: 'auto' | 'high' | 'medium' | 'low';
  language: string;
  createdAt: string;
  updatedAt: string;
}

// 同步相關類型
export interface SyncStatus {
  isOnline: boolean;
  lastSyncTime: Date | null;
  pendingChanges: number;
  isSyncing: boolean;
}
