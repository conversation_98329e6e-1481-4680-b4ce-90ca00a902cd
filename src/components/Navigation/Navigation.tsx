import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import {
  HomeIcon,
  ListBulletIcon,
  PlayIcon,
  Cog6ToothIcon,
  BeakerIcon,
  ArrowRightOnRectangleIcon,
  CloudIcon
} from '@heroicons/react/24/outline';
import {
  HomeIcon as HomeSolidIcon,
  ListBulletIcon as ListSolidIcon,
  PlayIcon as PlaySolidIcon,
  Cog6ToothIcon as CogSolidIcon,
  HeartIcon as HeartSolidIcon,
  BeakerIcon as BeakerSolidIcon,
  UserIcon as UserSolidIcon
} from '@heroicons/react/24/solid';
import { useAppStore } from '../../stores/appStore';
import { usePlayerStore } from '../../stores/playerStore';
import { useAuthStore } from '../../stores/authStore';

export const Navigation: React.FC = () => {
  const location = useLocation();
  const { currentPlaylist, favoriteChannels, syncStatus } = useAppStore();
  const { currentChannel } = usePlayerStore();
  const { user, isAuthenticated, signOut } = useAuthStore();
  const [showUserMenu, setShowUserMenu] = useState(false);

  const isActive = (path: string) => location.pathname === path;

  const navItems = [
    {
      path: '/',
      label: '首頁',
      icon: HomeIcon,
      activeIcon: HomeSolidIcon,
    },
    {
      path: '/channels',
      label: '頻道',
      icon: ListBulletIcon,
      activeIcon: ListSolidIcon,
      disabled: !currentPlaylist,
      badge: currentPlaylist?.totalChannels
    },
    {
      path: '/player',
      label: '播放器',
      icon: PlayIcon,
      activeIcon: PlaySolidIcon,
      disabled: !currentChannel,
    },
    {
      path: '/test',
      label: 'M3U 測試',
      icon: BeakerIcon,
      activeIcon: BeakerSolidIcon,
    },
    {
      path: '/settings',
      label: '設定',
      icon: Cog6ToothIcon,
      activeIcon: CogSolidIcon,
    }
  ];

  // 在播放器頁面、登入頁面和註冊頁面時隱藏導航
  if (location.pathname === '/player' || location.pathname === '/login' || location.pathname === '/register') {
    return null;
  }

  const handleSignOut = async () => {
    await signOut();
    setShowUserMenu(false);
  };

  return (
    <nav className="bg-white shadow-sm border-b border-gray-200">
      <div className="max-w-7xl mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link to="/" className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
              <PlaySolidIcon className="h-5 w-5 text-white" />
            </div>
            <span className="text-xl font-bold text-gray-900">TVBOX</span>
          </Link>

          {/* 導航項目 */}
          <div className="flex items-center space-x-1">
            {navItems.map((item) => {
              const Icon = isActive(item.path) ? item.activeIcon : item.icon;
              const active = isActive(item.path);

              return (
                <Link
                  key={item.path}
                  to={item.path}
                  className={`
                    relative flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200
                    ${active
                      ? 'bg-primary-100 text-primary-700'
                      : item.disabled
                        ? 'text-gray-400 cursor-not-allowed'
                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                    }
                  `}
                  onClick={(e) => {
                    if (item.disabled) {
                      e.preventDefault();
                    }
                  }}
                >
                  <Icon className="h-5 w-5 mr-2" />
                  {item.label}

                  {/* 徽章 */}
                  {item.badge && (
                    <span className="ml-2 px-2 py-0.5 text-xs bg-gray-200 text-gray-700 rounded-full">
                      {item.badge}
                    </span>
                  )}
                </Link>
              );
            })}

            {/* 收藏頻道指示器 */}
            {favoriteChannels.length > 0 && (
              <div className="flex items-center px-3 py-2 text-sm text-gray-600">
                <HeartSolidIcon className="h-4 w-4 text-red-500 mr-1" />
                <span className="text-xs">{favoriteChannels.length}</span>
              </div>
            )}

            {/* 同步狀態指示器 */}
            {isAuthenticated && (
              <div className="flex items-center px-2 py-2">
                <CloudIcon
                  className={`h-4 w-4 ${
                    syncStatus.isOnline
                      ? syncStatus.isSyncing
                        ? 'text-blue-500 animate-pulse'
                        : 'text-green-500'
                      : 'text-gray-400'
                  }`}
                />
              </div>
            )}

            {/* 用戶菜單 */}
            {isAuthenticated ? (
              <div className="relative">
                <button
                  onClick={() => setShowUserMenu(!showUserMenu)}
                  className="flex items-center px-3 py-2 rounded-lg text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-100"
                >
                  <UserSolidIcon className="h-5 w-5 mr-2" />
                  {user?.username || user?.email}
                </button>

                {showUserMenu && (
                  <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50">
                    <div className="px-4 py-2 text-sm text-gray-700 border-b">
                      <div className="font-medium">{user?.username}</div>
                      <div className="text-xs text-gray-500">{user?.email}</div>
                    </div>

                    <button
                      onClick={handleSignOut}
                      className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      <ArrowRightOnRectangleIcon className="h-4 w-4 mr-2" />
                      登出
                    </button>
                  </div>
                )}
              </div>
            ) : (
              <div className="flex items-center space-x-2">
                <Link
                  to="/login"
                  className="px-3 py-2 text-sm font-medium text-gray-600 hover:text-gray-900"
                >
                  登入
                </Link>
                <Link
                  to="/register"
                  className="px-3 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-lg"
                >
                  註冊
                </Link>
              </div>
            )}
          </div>
        </div>
      </div>
    </nav>
  );
};
