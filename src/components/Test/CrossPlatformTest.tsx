import React, { useState } from 'react';
import { MobileOptimization } from '../../utils/mobileOptimization';
import { CorsProxy } from '../../utils/corsProxy';

export const CrossPlatformTest: React.FC = () => {
  const [testResults, setTestResults] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);

  const runTests = async () => {
    setIsLoading(true);
    const results = {
      deviceInfo: MobileOptimization.getDiagnosticInfo(),
      hlsConfig: MobileOptimization.getOptimizedHLSConfig(),
      videoAttributes: MobileOptimization.getOptimizedVideoAttributes(),
      corsTest: null as any,
      timestamp: new Date().toISOString()
    };

    // 測試 CORS 代理
    try {
      const testUrl = 'https://smart.pendy.dpdns.org/test';
      const needsProxy = CorsProxy.needsProxy(testUrl);
      results.corsTest = {
        testUrl,
        needsProxy,
        proxyUrl: needsProxy ? CorsProxy.getProxyUrl(testUrl) : null
      };
    } catch (error) {
      results.corsTest = {
        error: error instanceof Error ? error.message : '未知錯誤'
      };
    }

    setTestResults(results);
    setIsLoading(false);
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">跨平台播放器測試</h1>
      
      <div className="mb-6">
        <button
          onClick={runTests}
          disabled={isLoading}
          className="bg-blue-500 hover:bg-blue-600 disabled:bg-gray-400 text-white px-4 py-2 rounded"
        >
          {isLoading ? '測試中...' : '運行測試'}
        </button>
      </div>

      {testResults && (
        <div className="space-y-6">
          {/* 設備資訊 */}
          <div className="bg-white border rounded-lg p-4">
            <h2 className="text-lg font-semibold mb-3">設備資訊</h2>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <strong>設備類型:</strong> {testResults.deviceInfo.isMobile ? '移動設備' : '桌面設備'}
              </div>
              <div>
                <strong>操作系統:</strong> {
                  testResults.deviceInfo.isIOS ? 'iOS' :
                  testResults.deviceInfo.isAndroid ? 'Android' : '其他'
                }
              </div>
              <div>
                <strong>瀏覽器:</strong> {
                  testResults.deviceInfo.isSafari ? 'Safari' :
                  testResults.deviceInfo.isChromeMobile ? 'Chrome Mobile' :
                  testResults.deviceInfo.isWeChat ? '微信' : '其他'
                }
              </div>
              <div>
                <strong>最佳策略:</strong> {testResults.deviceInfo.bestStrategy}
              </div>
              <div>
                <strong>HLS 支援:</strong> {testResults.deviceInfo.supportsHLS ? '✅' : '❌'}
              </div>
              <div>
                <strong>MSE 支援:</strong> {testResults.deviceInfo.supportsMSE ? '✅' : '❌'}
              </div>
            </div>
          </div>

          {/* HLS 配置 */}
          <div className="bg-white border rounded-lg p-4">
            <h2 className="text-lg font-semibold mb-3">HLS 配置</h2>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <strong>緩衝長度:</strong> {testResults.hlsConfig.maxBufferLength}s
              </div>
              <div>
                <strong>最大緩衝:</strong> {testResults.hlsConfig.maxMaxBufferLength}s
              </div>
              <div>
                <strong>重試次數:</strong> {testResults.hlsConfig.maxRetry}
              </div>
              <div>
                <strong>超時時間:</strong> {testResults.hlsConfig.fragLoadingTimeOut}ms
              </div>
              <div>
                <strong>Web Worker:</strong> {testResults.hlsConfig.enableWorker ? '✅' : '❌'}
              </div>
              <div>
                <strong>軟體 AES:</strong> {testResults.hlsConfig.enableSoftwareAES ? '✅' : '❌'}
              </div>
            </div>
          </div>

          {/* Video 屬性 */}
          <div className="bg-white border rounded-lg p-4">
            <h2 className="text-lg font-semibold mb-3">Video 元素屬性</h2>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <strong>playsInline:</strong> {testResults.videoAttributes.playsInline ? '✅' : '❌'}
              </div>
              <div>
                <strong>preload:</strong> {testResults.videoAttributes.preload}
              </div>
              <div>
                <strong>crossOrigin:</strong> {testResults.videoAttributes.crossOrigin}
              </div>
              <div>
                <strong>controls:</strong> {testResults.videoAttributes.controls ? '✅' : '❌'}
              </div>
              {testResults.videoAttributes['webkit-playsinline'] && (
                <div>
                  <strong>webkit-playsinline:</strong> ✅
                </div>
              )}
              {testResults.videoAttributes['x5-video-player-type'] && (
                <div>
                  <strong>X5 優化:</strong> ✅
                </div>
              )}
            </div>
          </div>

          {/* CORS 測試 */}
          <div className="bg-white border rounded-lg p-4">
            <h2 className="text-lg font-semibold mb-3">CORS 測試</h2>
            {testResults.corsTest.error ? (
              <div className="text-red-600">
                <strong>錯誤:</strong> {testResults.corsTest.error}
              </div>
            ) : (
              <div className="space-y-2 text-sm">
                <div>
                  <strong>測試 URL:</strong> {testResults.corsTest.testUrl}
                </div>
                <div>
                  <strong>需要代理:</strong> {testResults.corsTest.needsProxy ? '✅' : '❌'}
                </div>
                {testResults.corsTest.proxyUrl && (
                  <div>
                    <strong>代理 URL:</strong> 
                    <div className="mt-1 p-2 bg-gray-100 rounded text-xs break-all">
                      {testResults.corsTest.proxyUrl}
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* 網路資訊 */}
          {testResults.deviceInfo.connection && (
            <div className="bg-white border rounded-lg p-4">
              <h2 className="text-lg font-semibold mb-3">網路資訊</h2>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <strong>連接類型:</strong> {testResults.deviceInfo.connection.effectiveType}
                </div>
                <div>
                  <strong>下載速度:</strong> {testResults.deviceInfo.connection.downlink} Mbps
                </div>
                <div>
                  <strong>延遲:</strong> {testResults.deviceInfo.connection.rtt} ms
                </div>
              </div>
            </div>
          )}

          {/* 原始數據 */}
          <details className="bg-gray-50 border rounded-lg p-4">
            <summary className="cursor-pointer font-semibold">原始測試數據</summary>
            <pre className="mt-3 text-xs overflow-auto">
              {JSON.stringify(testResults, null, 2)}
            </pre>
          </details>
        </div>
      )}
    </div>
  );
};
