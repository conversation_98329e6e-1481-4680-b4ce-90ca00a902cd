import React, { useState } from 'react';
import { 
  DevicePhoneMobileIcon, 
  ChevronDownIcon, 
  ChevronUpIcon,
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import { MobileOptimization } from '../../utils/mobileOptimization';

interface MobileDiagnosticsProps {
  isVisible?: boolean;
}

export const MobileDiagnostics: React.FC<MobileDiagnosticsProps> = ({
  isVisible = false
}) => {
  const [isExpanded, setIsExpanded] = useState(isVisible);
  const deviceInfo = MobileOptimization.getDiagnosticInfo();

  // 在所有平台都顯示，但標題會根據平台調整

  const StatusIcon = ({ status }: { status: boolean }) => {
    return status ? (
      <CheckCircleIcon className="h-4 w-4 text-green-500" />
    ) : (
      <XCircleIcon className="h-4 w-4 text-red-500" />
    );
  };

  const getConnectionQuality = () => {
    if (!deviceInfo.connection) return '未知';

    const effectiveType = deviceInfo.connection.effectiveType;
    switch (effectiveType) {
      case '4g':
        return '良好 (4G)';
      case '3g':
        return '一般 (3G)';
      case '2g':
        return '較差 (2G)';
      case 'slow-2g':
        return '很差 (慢速 2G)';
      default:
        return effectiveType || '未知';
    }
  };

  const getRecommendations = () => {
    const recommendations = [];

    if (deviceInfo.isWeChat) {
      recommendations.push({
        type: 'warning',
        message: '微信瀏覽器可能有播放限制，建議複製連結到外部瀏覽器'
      });
    }

    if (deviceInfo.isIOS && !deviceInfo.supportsHLS) {
      recommendations.push({
        type: 'error',
        message: 'iOS 設備不支援 HLS，可能無法播放某些串流'
      });
    }

    if (!deviceInfo.supportsMSE && deviceInfo.bestStrategy === 'hls.js') {
      recommendations.push({
        type: 'warning',
        message: '瀏覽器不支援 MSE，將使用原生播放器'
      });
    }

    if (deviceInfo.connection && deviceInfo.connection.effectiveType === '2g') {
      recommendations.push({
        type: 'warning',
        message: '網路連接較慢，建議切換到 WiFi 或 4G 網路'
      });
    }

    // 桌面端特殊建議
    if (!deviceInfo.isMobile) {
      if (deviceInfo.supportsMSE) {
        recommendations.push({
          type: 'success',
          message: '桌面端配置良好，支援 HLS.js 高級功能'
        });
      } else {
        recommendations.push({
          type: 'warning',
          message: '瀏覽器較舊，建議更新到最新版本以獲得更好的播放體驗'
        });
      }
    }

    if (recommendations.length === 0) {
      recommendations.push({
        type: 'success',
        message: deviceInfo.isMobile ? '移動端配置良好，應該可以正常播放' : '桌面端配置良好，應該可以正常播放'
      });
    }

    return recommendations;
  };

  return (
    <div className="bg-blue-50 border border-blue-200 rounded-lg mb-4">
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="w-full px-4 py-3 flex items-center justify-between text-left hover:bg-blue-100 transition-colors"
      >
        <div className="flex items-center">
          <DevicePhoneMobileIcon className="h-5 w-5 text-blue-500 mr-2" />
          <span className="text-sm font-medium text-blue-800">
            {deviceInfo.isMobile ? '移動端診斷資訊' : '桌面端診斷資訊'}
          </span>
        </div>
        {isExpanded ? (
          <ChevronUpIcon className="h-4 w-4 text-blue-500" />
        ) : (
          <ChevronDownIcon className="h-4 w-4 text-blue-500" />
        )}
      </button>

      {isExpanded && (
        <div className="px-4 pb-4 space-y-4">
          {/* 設備資訊 */}
          <div>
            <h4 className="text-sm font-medium text-blue-800 mb-2">設備資訊</h4>
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div className="flex items-center justify-between">
                <span className="text-blue-600">設備類型:</span>
                <span className="text-blue-800">
                  {deviceInfo.isMobile ?
                    (deviceInfo.isIOS ? 'iOS' : deviceInfo.isAndroid ? 'Android' : '移動設備') :
                    '桌面設備'}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-blue-600">瀏覽器:</span>
                <span className="text-blue-800">
                  {deviceInfo.isSafari ? 'Safari' :
                   deviceInfo.isChromeMobile ? 'Chrome Mobile' :
                   deviceInfo.isWeChat ? '微信' :
                   !deviceInfo.isMobile ? 'Desktop Browser' : '其他'}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-blue-600">螢幕尺寸:</span>
                <span className="text-blue-800">
                  {deviceInfo.viewport.width} × {deviceInfo.viewport.height}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-blue-600">像素比:</span>
                <span className="text-blue-800">
                  {deviceInfo.viewport.devicePixelRatio}x
                </span>
              </div>
            </div>
          </div>

          {/* 播放支援 */}
          <div>
            <h4 className="text-sm font-medium text-blue-800 mb-2">播放支援</h4>
            <div className="space-y-1 text-xs">
              <div className="flex items-center justify-between">
                <span className="text-blue-600">HLS 支援:</span>
                <div className="flex items-center">
                  <StatusIcon status={deviceInfo.supportsHLS} />
                  <span className="ml-1 text-blue-800">
                    {deviceInfo.supportsHLS ? '支援' : '不支援'}
                  </span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-blue-600">MSE 支援:</span>
                <div className="flex items-center">
                  <StatusIcon status={deviceInfo.supportsMSE} />
                  <span className="ml-1 text-blue-800">
                    {deviceInfo.supportsMSE ? '支援' : '不支援'}
                  </span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-blue-600">最佳策略:</span>
                <span className="text-blue-800 capitalize">
                  {deviceInfo.bestStrategy}
                </span>
              </div>
            </div>
          </div>

          {/* 網路資訊 */}
          {deviceInfo.connection && (
            <div>
              <h4 className="text-sm font-medium text-blue-800 mb-2">網路資訊</h4>
              <div className="space-y-1 text-xs">
                <div className="flex items-center justify-between">
                  <span className="text-blue-600">連接品質:</span>
                  <span className="text-blue-800">{getConnectionQuality()}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-blue-600">下載速度:</span>
                  <span className="text-blue-800">
                    {deviceInfo.connection.downlink} Mbps
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-blue-600">延遲:</span>
                  <span className="text-blue-800">
                    {deviceInfo.connection.rtt} ms
                  </span>
                </div>
              </div>
            </div>
          )}

          {/* 建議 */}
          <div>
            <h4 className="text-sm font-medium text-blue-800 mb-2">建議</h4>
            <div className="space-y-2">
              {getRecommendations().map((rec, index) => (
                <div 
                  key={index}
                  className={`flex items-start p-2 rounded text-xs ${
                    rec.type === 'success' ? 'bg-green-100 text-green-700' :
                    rec.type === 'warning' ? 'bg-yellow-100 text-yellow-700' :
                    'bg-red-100 text-red-700'
                  }`}
                >
                  {rec.type === 'success' ? (
                    <CheckCircleIcon className="h-4 w-4 mr-2 flex-shrink-0 mt-0.5" />
                  ) : rec.type === 'warning' ? (
                    <ExclamationTriangleIcon className="h-4 w-4 mr-2 flex-shrink-0 mt-0.5" />
                  ) : (
                    <XCircleIcon className="h-4 w-4 mr-2 flex-shrink-0 mt-0.5" />
                  )}
                  <span>{rec.message}</span>
                </div>
              ))}
            </div>
          </div>

          {/* User Agent */}
          <div>
            <h4 className="text-sm font-medium text-blue-800 mb-2">User Agent</h4>
            <div className="text-xs text-blue-600 bg-blue-100 p-2 rounded break-all">
              {deviceInfo.userAgent}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
