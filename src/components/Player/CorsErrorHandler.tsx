import React from 'react';
import { ExclamationTriangleIcon, ArrowPathIcon, DevicePhoneMobileIcon } from '@heroicons/react/24/outline';
import { MobileOptimization } from '../../utils/mobileOptimization';

interface CorsErrorHandlerProps {
  error: string;
  channelUrl: string;
  onRetry: () => void;
  onDismiss: () => void;
}

export const CorsErrorHandler: React.FC<CorsErrorHandlerProps> = ({
  error,
  channelUrl,
  onRetry,
  onDismiss
}) => {
  const isCorsError = error.toLowerCase().includes('cors') ||
                     error.includes('跨域') ||
                     error.includes('無法載入播放清單') ||
                     error.includes('網路錯誤');

  // 獲取設備資訊
  const deviceInfo = MobileOptimization.getDiagnosticInfo();
  const isMobile = deviceInfo.isMobile;

  if (!isCorsError) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
        <div className="flex items-start">
          <ExclamationTriangleIcon className="h-5 w-5 text-red-400 mt-0.5 mr-3 flex-shrink-0" />
          <div className="flex-1">
            <h3 className="text-sm font-medium text-red-800">播放錯誤</h3>
            <p className="text-sm text-red-700 mt-1">{error}</p>
            <div className="mt-3 flex space-x-2">
              <button
                onClick={onRetry}
                className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
              >
                <ArrowPathIcon className="h-4 w-4 mr-1" />
                重試
              </button>
              <button
                onClick={onDismiss}
                className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
              >
                關閉
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-orange-50 border border-orange-200 rounded-lg p-4 mb-4">
      <div className="flex items-start">
        <ExclamationTriangleIcon className="h-5 w-5 text-orange-400 mt-0.5 mr-3 flex-shrink-0" />
        <div className="flex-1">
          <h3 className="text-sm font-medium text-orange-800">CORS 跨域限制</h3>
          <p className="text-sm text-orange-700 mt-1">
            無法載入播放清單，可能是 CORS 限制或伺服器無法存取。
          </p>
          
          <div className="mt-3 space-y-2">
            <div className="text-xs text-orange-600">
              <strong>可能的解決方案：</strong>
            </div>
            <ul className="text-xs text-orange-600 space-y-1 ml-4">
              <li>• 系統正在嘗試使用代理服務器</li>
              <li>• 請檢查網路連接是否正常</li>
              <li>• 嘗試使用其他播放清單來源</li>
              {isMobile && (
                <>
                  <li>• 移動端建議使用 WiFi 連接</li>
                  {deviceInfo.isWeChat && (
                    <li>• 微信瀏覽器限制：建議複製連結到外部瀏覽器</li>
                  )}
                  {deviceInfo.isIOS && (
                    <li>• iOS 設備：請確保允許跨域請求</li>
                  )}
                </>
              )}
              <li>• 某些頻道可能需要特殊的網路環境</li>
            </ul>
          </div>

          <div className="mt-4 flex space-x-2">
            <button
              onClick={onRetry}
              className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-orange-700 bg-orange-100 hover:bg-orange-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
            >
              <ArrowPathIcon className="h-4 w-4 mr-1" />
              重新嘗試
            </button>
            <button
              onClick={onDismiss}
              className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-orange-700 bg-orange-100 hover:bg-orange-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
            >
              關閉
            </button>
          </div>

          <div className="mt-3 p-2 bg-orange-100 rounded text-xs text-orange-600">
            <strong>技術說明：</strong> CORS (跨域資源共享) 是瀏覽器的安全機制，
            防止網站載入來自其他域名的資源。我們正在使用代理服務器來嘗試解決此問題。
            <div className="mt-2 flex items-center">
              <DevicePhoneMobileIcon className="h-4 w-4 mr-1" />
              <span>
                檢測到{isMobile ? '移動' : '桌面'}設備 ({deviceInfo.isIOS ? 'iOS' : deviceInfo.isAndroid ? 'Android' : deviceInfo.isMobile ? '移動設備' : '桌面設備'})，
                已啟用{isMobile ? '移動端' : '桌面端'}優化配置。
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
