# Supabase 資料庫設置指南

## 概述
本指南將幫助您完成 TVBOX 專案與 Supabase PostgreSQL 資料庫的整合設置。

## 前置準備
1. 確保您已經有 Supabase 帳戶
2. 已創建新的 Supabase 專案
3. 獲得資料庫連接資訊

## 設置步驟

### 1. 更新環境變數
編輯 `.env` 檔案，更新以下資訊：

```env
# 將以下 URL 替換為您的實際 Supabase 專案 URL
VITE_SUPABASE_URL=https://your-project-ref.supabase.co

# 將以下 Key 替換為您的實際 anon key
VITE_SUPABASE_ANON_KEY=your-anon-key-here
```

**如何獲取這些資訊：**
1. 登入 [Supabase Dashboard](https://app.supabase.com)
2. 選擇您的專案
3. 前往 Settings > API
4. 複製 Project URL 和 anon public key

### 2. 執行資料庫架構
1. 在 Supabase Dashboard 中，前往 SQL Editor
2. 複製 `database/schema.sql` 檔案的內容
3. 貼上到 SQL Editor 中並執行

這將創建以下資料表：
- `users` - 用戶資料
- `playlists` - 播放清單
- `channels` - 頻道資料
- `favorite_channels` - 收藏頻道
- `user_settings` - 用戶設定

### 3. 設定認證
在 Supabase Dashboard 中：
1. 前往 Authentication > Settings
2. 確保 "Enable email confirmations" 根據您的需求設定
3. 可以設定自定義的 SMTP 設定（可選）

### 4. 測試連接
1. 啟動開發伺服器：`npm run dev`
2. 嘗試註冊新帳戶
3. 測試登入功能
4. 檢查資料是否正確儲存到 Supabase

## 功能特色

### 已實現的功能
✅ 用戶註冊和登入  
✅ 播放清單雲端同步  
✅ 收藏頻道同步  
✅ 用戶設定同步  
✅ 離線/在線狀態檢測  
✅ 自動資料同步  
✅ Row Level Security (RLS) 安全性  

### 資料同步機制
- **本地優先**：所有資料先儲存到本地 localStorage
- **雲端同步**：當用戶登入且有網路連接時，自動同步到雲端
- **衝突處理**：雲端資料優先，本地資料作為備份

### 安全性
- 使用 Supabase 的 Row Level Security (RLS)
- 用戶只能存取自己的資料
- 支援公開播放清單分享（可選）

## 使用方式

### 用戶認證
```typescript
import { useAuthStore } from './stores/authStore';

const { signUp, signIn, signOut, user, isAuthenticated } = useAuthStore();

// 註冊
await signUp('<EMAIL>', 'password', 'username');

// 登入
await signIn('<EMAIL>', 'password');

// 登出
await signOut();
```

### 資料同步
```typescript
import { useAppStore } from './stores/appStore';

const { 
  savePlaylistToCloud, 
  syncFavoriteChannels, 
  syncUserSettings,
  syncAllData 
} = useAppStore();

// 儲存播放清單到雲端
await savePlaylistToCloud(playlist);

// 同步所有資料
await syncAllData();
```

## 故障排除

### 常見問題

**1. 連接失敗**
- 檢查 `.env` 檔案中的 URL 和 Key 是否正確
- 確認 Supabase 專案狀態正常

**2. 認證失敗**
- 檢查 Supabase 認證設定
- 確認電子郵件確認設定

**3. 資料同步問題**
- 檢查網路連接
- 查看瀏覽器開發者工具的 Console 錯誤訊息
- 確認 RLS 政策設定正確

**4. 權限錯誤**
- 確認已執行完整的 `database/schema.sql` 腳本
- 檢查 RLS 政策是否正確設定

### 除錯技巧
1. 開啟瀏覽器開發者工具
2. 查看 Network 標籤中的 API 請求
3. 檢查 Console 中的錯誤訊息
4. 在 Supabase Dashboard 中查看 Logs

## 進階設定

### 自定義 RLS 政策
如需修改資料存取權限，可以在 Supabase SQL Editor 中調整 RLS 政策。

### 效能優化
- 資料庫索引已預先設定
- 考慮實作資料分頁（大量頻道時）
- 使用 Supabase 的即時訂閱功能（可選）

### 備份策略
- Supabase 提供自動備份
- 本地 localStorage 作為離線備份
- 考慮定期匯出重要資料

## 支援
如遇到問題，請檢查：
1. [Supabase 官方文件](https://supabase.com/docs)
2. 專案的 GitHub Issues
3. Supabase 社群論壇
