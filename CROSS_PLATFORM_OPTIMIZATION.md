# 跨平台播放器優化指南

## 概述

本項目已針對桌面端和移動端（特別是 iOS 和 Android）進行了全面的跨平台優化，確保在所有設備上都能正常觀看 IPTV 串流。

## 🎯 主要優化功能

### 1. 智能播放策略選擇

系統會自動檢測設備類型並選擇最佳播放策略：

- **桌面端 Chrome/Firefox/Edge**: 使用 HLS.js（支援 MSE）
- **iOS Safari**: 使用原生 HLS 播放（更穩定）
- **Android Chrome**: 使用 HLS.js（如果支援 MSE）
- **其他瀏覽器**: 根據支援能力自動選擇

### 2. 跨平台 HLS 配置

#### 桌面端配置
- 更大的緩衝區（60-120秒）
- 更多重試次數（5次）
- 更長的超時時間（30秒）
- 啟用頻寬測試

#### 移動端配置
- 較小的緩衝區（30-60秒）節省記憶體
- 較少重試次數（3次）節省電量
- 適中的超時時間（20秒）
- 關閉頻寬測試

### 3. CORS 跨域解決方案

#### 智能代理檢測
系統會自動檢測以下域名並使用代理：
- `smart.pendy.dpdns.org`（您的八大娛樂 GTV）
- `files.catbox.moe`
- `raw.githubusercontent.com`
- `koyeb.app`
- 其他常見的 IPTV 域名

#### 多重代理備援
- 按穩定性排序的代理服務器列表
- 自動切換到下一個可用代理
- 最終備用：直接請求

### 4. 移動端特殊優化

#### iOS 設備
- 使用 Safari 原生 HLS 播放
- 設定 `webkit-playsinline` 屬性
- 禁用自動播放（需用戶互動）
- 優化記憶體使用

#### Android 設備
- 支援騰訊 X5 內核優化
- 設定 `x5-video-player-type="h5"`
- 支援硬體加速
- 更大的緩衝區配置

#### 微信瀏覽器
- 特殊錯誤提示
- 建議複製連結到外部瀏覽器

### 5. 跨平台診斷工具

#### 設備檢測
- 自動檢測設備類型（桌面/移動）
- 識別操作系統（iOS/Android/其他）
- 檢測瀏覽器類型
- 檢測播放支援能力

#### 網路監控
- 連接品質檢測
- 下載速度監控
- 延遲測試
- 自動調整策略

## 🔧 技術實現

### 核心文件

1. **`src/utils/mobileOptimization.ts`**
   - 跨平台設備檢測
   - 智能播放策略選擇
   - 平台特定配置生成

2. **`src/components/Player/HLSPlayer.tsx`**
   - 統一的播放器組件
   - 自動策略選擇
   - 跨平台錯誤處理

3. **`src/utils/corsProxy.ts`**
   - CORS 代理管理
   - 智能域名檢測
   - 多重備援機制

### 配置示例

```typescript
// 自動獲取平台優化配置
const platformConfig = MobileOptimization.getOptimizedHLSConfig();
const videoAttributes = MobileOptimization.getOptimizedVideoAttributes();
const playbackStrategy = MobileOptimization.getBestPlaybackStrategy();
```

## 📱 移動端使用指南

### iOS 設備
1. 建議使用 Safari 瀏覽器
2. 確保網路連接穩定
3. 首次播放需要手動點擊
4. 如遇問題可嘗試重新載入

### Android 設備
1. 建議使用 Chrome 瀏覽器
2. 啟用硬體加速（如果可用）
3. 使用 WiFi 或 4G 網路
4. 避免使用過舊的瀏覽器版本

### 微信瀏覽器
1. 複製連結到外部瀏覽器
2. 推薦使用 Safari（iOS）或 Chrome（Android）
3. 避免在微信內直接播放

## 🖥️ 桌面端使用指南

### 推薦瀏覽器
1. **Chrome** - 最佳支援
2. **Firefox** - 良好支援
3. **Edge** - 良好支援
4. **Safari** - 基本支援

### 優化建議
1. 更新瀏覽器到最新版本
2. 啟用硬體加速
3. 確保網路連接穩定
4. 關閉不必要的瀏覽器擴展

## 🔍 故障排除

### 常見問題

1. **CORS 錯誤**
   - 系統會自動使用代理解決
   - 檢查網路連接
   - 嘗試重新載入

2. **播放失敗**
   - 檢查瀏覽器支援
   - 嘗試其他瀏覽器
   - 查看診斷資訊

3. **載入緩慢**
   - 檢查網路速度
   - 切換到 WiFi
   - 清除瀏覽器快取

### 診斷工具

訪問 `/cross-platform-test` 頁面可以：
- 檢測設備能力
- 測試 CORS 配置
- 查看詳細診斷資訊
- 驗證播放策略

## 📊 測試結果

### 支援的平台
- ✅ Windows Chrome/Firefox/Edge
- ✅ macOS Safari/Chrome/Firefox
- ✅ iOS Safari
- ✅ Android Chrome
- ✅ iPad Safari
- ⚠️ 微信瀏覽器（建議外部瀏覽器）

### 支援的格式
- ✅ HLS (.m3u8)
- ✅ MP4
- ✅ WebM
- ✅ 其他常見影片格式

## 🚀 性能優化

### 記憶體使用
- 移動端：30-60MB 緩衝
- 桌面端：60-120MB 緩衝
- 自動垃圾回收

### 網路優化
- 智能頻寬檢測
- 自適應品質調整
- 預載入優化

### 電量優化
- 移動端關閉低延遲模式
- 減少重試次數
- 優化緩衝策略

## 📝 更新日誌

### v1.0.0 (2024-12-06)
- ✅ 完成跨平台播放器優化
- ✅ 新增智能播放策略選擇
- ✅ 優化 CORS 代理處理
- ✅ 新增移動端特殊優化
- ✅ 新增跨平台診斷工具
- ✅ 完善錯誤處理機制

---

**注意**: 本優化確保在電腦和手機上都能正常觀看，包括您提到的八大娛樂 GTV 等頻道。如遇任何問題，請查看診斷資訊或聯繫技術支援。
