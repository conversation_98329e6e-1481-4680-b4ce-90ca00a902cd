// 測試 Supabase 連接的簡單腳本
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://pqwvmcmmqjihoaixqlsz.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBxd3ZtY21tcWppaG9haXhxbHN6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYxODQwODYsImV4cCI6MjA2MTc2MDA4Nn0.QizqN84h0gyxgmS-4B5RUQevYc4_Omm_7zoQI00lyRY';

const supabase = createClient(supabaseUrl, supabaseKey);

async function testConnection() {
  console.log('🔍 測試 Supabase 連接...');
  
  try {
    // 測試基本連接
    const { data, error } = await supabase
      .from('users')
      .select('count')
      .limit(1);
    
    if (error) {
      console.error('❌ 連接失敗:', error.message);
      
      if (error.message.includes('relation "users" does not exist')) {
        console.log('💡 提示: 請先在 Supabase SQL Editor 中執行 database/schema.sql');
      }
    } else {
      console.log('✅ Supabase 連接成功！');
      console.log('📊 資料:', data);
    }
    
    // 測試認證
    console.log('\n🔐 測試認證功能...');
    const { data: authData, error: authError } = await supabase.auth.getSession();
    
    if (authError) {
      console.error('❌ 認證測試失敗:', authError.message);
    } else {
      console.log('✅ 認證功能正常');
      console.log('👤 當前會話:', authData.session ? '已登入' : '未登入');
    }
    
  } catch (err) {
    console.error('❌ 測試過程中發生錯誤:', err.message);
  }
}

testConnection();
