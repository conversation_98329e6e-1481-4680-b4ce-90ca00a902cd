# CORS 跨域問題解決指南

## 什麼是 CORS？

CORS (Cross-Origin Resource Sharing，跨域資源共享) 是瀏覽器的一項安全機制，用來防止網站載入來自其他域名的資源。當您看到 "CORS 限制" 錯誤時，這表示瀏覽器阻止了對外部串流源的訪問。

## 常見的 CORS 錯誤訊息

- "無法載入播放清單：未知錯誤，可能是 CORS 限制或伺服器無法存取"
- "Access to fetch at '...' from origin '...' has been blocked by CORS policy"
- "網路錯誤"
- "載入失敗"

## 我們的解決方案

### 1. 自動代理檢測
系統會自動檢測是否需要使用代理服務器：
- 檢查串流 URL 的域名
- 如果檢測到 CORS 限制，自動嘗試使用代理

### 2. 多重代理服務器
我們使用多個公共代理服務器：
- api.allorigins.win
- cors-anywhere.herokuapp.com
- thingproxy.freeboard.io
- corsproxy.io
- 等等...

### 3. 智能重試機制
- 如果一個代理失敗，自動嘗試下一個
- 最多嘗試所有可用的代理服務器
- 如果所有代理都失敗，嘗試直接連接

## 用戶操作指南

### 當遇到 CORS 錯誤時：

1. **點擊重新嘗試**
   - 系統會自動嘗試不同的代理服務器
   - 通常第二次嘗試會成功

2. **檢查網路連接**
   - 確保您的網路連接正常
   - 嘗試重新整理頁面

3. **嘗試其他播放清單**
   - 某些串流源可能永久無法訪問
   - 嘗試使用其他的 M3U 播放清單

4. **使用不同的瀏覽器**
   - Chrome、Firefox、Safari 對 CORS 的處理略有不同
   - 某些瀏覽器可能有更寬鬆的政策

## 技術細節

### 代理服務器的工作原理
```
您的瀏覽器 → 代理服務器 → 串流源
```

代理服務器充當中介，幫助您的瀏覽器訪問被 CORS 限制的資源。

### 自動檢測邏輯
系統會檢查以下域名是否需要代理：
- files.catbox.moe
- raw.githubusercontent.com
- breezy-audrie (Koyeb 服務)
- koyeb.app
- 其他已知的限制域名

### HLS.js 配置優化
- 禁用 Web Worker 以避免 CORS 問題
- 設置適當的 XHR 標頭
- 使用匿名 CORS 模式

## 開發者說明

### 如何添加新的代理服務器
在 `src/utils/corsProxy.ts` 中添加：
```typescript
private static proxies = [
  // 現有代理...
  'https://your-new-proxy.com/?url=',
];
```

### 如何添加需要代理的域名
在 `needsProxyDomains` 數組中添加：
```typescript
const needsProxyDomains = [
  // 現有域名...
  'your-domain.com',
];
```

## 限制和注意事項

1. **代理服務器可能不穩定**
   - 公共代理服務器可能會暫時不可用
   - 這是免費服務的正常現象

2. **某些串流源無法代理**
   - 有些服務器會檢測並阻止代理訪問
   - 這種情況下無法播放

3. **播放品質可能受影響**
   - 通過代理的串流可能會有延遲
   - 建議使用直接支援 CORS 的串流源

## 常見問題解答

**Q: 為什麼有些頻道可以播放，有些不行？**
A: 這取決於串流源的服務器配置。有些允許跨域訪問，有些不允許。

**Q: 代理會影響播放品質嗎？**
A: 可能會有輕微影響，但通常不明顯。代理服務器的速度和穩定性會影響播放體驗。

**Q: 可以自己架設代理服務器嗎？**
A: 可以，但需要技術知識。您可以使用 cors-anywhere 或類似的開源解決方案。

**Q: 為什麼不直接關閉 CORS？**
A: CORS 是瀏覽器的安全機制，無法在客戶端關閉。這是為了保護用戶安全。

## 聯繫支援

如果您持續遇到 CORS 問題，請：
1. 檢查瀏覽器控制台的詳細錯誤訊息
2. 嘗試不同的播放清單來源
3. 報告問題時請提供具體的錯誤訊息和 URL
