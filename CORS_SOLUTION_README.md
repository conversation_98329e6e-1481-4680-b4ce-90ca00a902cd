# CORS 跨域問題解決方案

## 問題描述

在手機瀏覽器中播放視頻時，可能會遇到 CORS（跨域資源共享）錯誤。這是瀏覽器的安全機制，防止網站載入來自其他域名的資源。

## 已實施的解決方案

### 1. 服務器端配置

#### Netlify 配置 (`netlify.toml`)
- 添加了全域 CORS 標頭
- 針對視頻文件（.m3u8, .ts）的特殊 CORS 設定
- 代理重定向配置

#### Vite 開發服務器配置 (`vite.config.ts`)
- 開發環境的 CORS 設定
- 代理配置來處理跨域請求

### 2. 客戶端優化

#### HLS 播放器改進 (`src/components/Player/HLSPlayer.tsx`)
- 改進的 XHR 設定來處理 CORS 問題
- 禁用 Worker 模式以減少 CORS 問題
- 增強的錯誤處理和重試機制
- 智能 URL 處理

#### 視頻流代理服務 (`src/utils/videoStreamProxy.ts`)
- 自動檢測需要代理的 URL
- 多個代理服務器備援
- 智能代理選擇和健康檢查
- 自動重試機制

#### CORS 錯誤處理界面 (`src/components/Player/CorsErrorHandler.tsx`)
- 用戶友好的錯誤信息顯示
- 詳細的解決方案建議
- 一鍵重試功能

### 3. 視頻元素優化

- 設定 `crossOrigin="anonymous"`
- 優化的 preload 策略
- 多格式支援（HLS, MP4, WebM）
- 手機瀏覽器特殊屬性支援

## 使用方法

### 自動處理
系統會自動：
1. 檢測視頻 URL 是否需要代理
2. 嘗試直接訪問
3. 如果失敗，自動使用代理服務
4. 提供用戶友好的錯誤信息

### 手動處理
如果自動處理失敗，用戶可以：
1. 點擊「重新嘗試」按鈕
2. 嘗試使用其他瀏覽器
3. 檢查網路連線

## 支援的代理服務

1. `api.allorigins.win` - 主要代理服務
2. `corsproxy.io` - 備用代理
3. `cors.bridged.cc` - 備用代理
4. `api.codetabs.com` - 備用代理
5. `thingproxy.freeboard.io` - 最後備援

## 瀏覽器相容性

### 完全支援
- Chrome (Android/Desktop)
- Firefox (Android/Desktop)
- Safari (iOS/macOS)
- Edge (Android/Desktop)

### 部分支援
- 某些舊版瀏覽器可能需要手動重試
- 部分企業網路環境可能有額外限制

## 故障排除

### 常見問題

1. **視頻無法載入**
   - 檢查網路連線
   - 嘗試重新整理頁面
   - 使用其他瀏覽器

2. **載入速度慢**
   - 代理服務器可能較慢
   - 嘗試等待或重試

3. **某些頻道無法播放**
   - 可能是源服務器問題
   - 嘗試其他頻道或播放清單

### 開發者調試

1. 開啟瀏覽器開發者工具
2. 查看 Console 標籤的錯誤信息
3. 查看 Network 標籤的請求狀態
4. 檢查是否有 CORS 相關錯誤

## 技術細節

### CORS 政策
- 使用 `Access-Control-Allow-Origin: *`
- 支援 `GET`, `HEAD`, `OPTIONS` 方法
- 允許 `Range`, `Content-Type`, `Accept` 標頭

### 代理機制
- 自動健康檢查
- 失敗自動切換
- 智能重試策略

### 性能優化
- 預載入策略優化
- 緩衝區設定調整
- 網路條件自適應

## 更新日誌

### v1.0.0 (2024-01-XX)
- 初始 CORS 解決方案實施
- 多代理服務器支援
- 用戶友好錯誤處理
- 自動重試機制

## 貢獻

如果您發現新的 CORS 問題或有改進建議，請：
1. 提交 Issue 描述問題
2. 提供錯誤日誌和瀏覽器信息
3. 建議可能的解決方案

## 授權

本解決方案遵循 MIT 授權條款。
