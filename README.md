# TVBOX - IPTV 串流播放平台

TVBOX 是一款針對 IPTV 使用者設計的現代化影音串流播放平台，支援 .m3u / .m3u8 等格式的直播清單。

## 🆕 新功能：Supabase 雲端同步

現在支援 Supabase PostgreSQL 資料庫整合，提供：
- 🔐 **用戶認證**: 註冊、登入、登出功能
- ☁️ **雲端同步**: 播放清單、收藏頻道、用戶設定跨裝置同步
- 📱 **離線支援**: 本地儲存作為備份，自動同步到雲端
- 🔒 **資料安全**: Row Level Security (RLS) 保護用戶資料
- 🌐 **即時狀態**: 顯示在線/離線和同步狀態

> 📋 **設置說明**: 請參考 [SUPABASE_SETUP.md](./SUPABASE_SETUP.md) 了解如何配置 Supabase 資料庫

## ✨ 功能特色

- 🎥 **多格式支援**: 支援 m3u、m3u8、HLS 等常見串流格式
- 📱 **響應式設計**: 適配桌面和行動裝置
- 🔍 **智慧搜尋**: 快速搜尋和過濾頻道
- ❤️ **收藏功能**: 收藏喜愛的頻道
- 📋 **播放清單管理**: 管理多個播放清單
- 📱 **QR 掃描**: 快速匯入播放清單
- 🎨 **現代化 UI**: 使用 TailwindCSS 設計
- 💾 **本地儲存**: 自動儲存設定和收藏

## 🚀 快速開始

### 安裝依賴
```bash
npm install
```

### 啟動開發伺服器
```bash
npm run dev
```

### 建構生產版本
```bash
npm run build
```

## 📖 使用說明

### 1. 載入播放清單
- 在首頁輸入 .m3u 或 .m3u8 播放清單的 URL
- 支援 GitHub raw 連結、直播源等
- 或使用 QR 掃描功能快速匯入

### 2. 瀏覽頻道
- 載入成功後會顯示所有可用頻道
- 支援按群組分類和搜尋功能
- 點擊頻道卡片即可開始播放

### 3. 播放控制
- 支援播放/暫停、音量調節
- 全螢幕播放模式
- 進度條控制

### 4. 收藏管理
- 點擊愛心圖示收藏頻道
- 在設定頁面查看收藏統計

## 🛠️ 技術棧

- **前端框架**: React 18 + TypeScript
- **建構工具**: Vite
- **UI 框架**: TailwindCSS + Headless UI
- **播放器**: HLS.js
- **狀態管理**: Zustand
- **路由**: React Router
- **QR 掃描**: html5-qrcode
- **圖示**: Heroicons
- **資料庫**: Supabase PostgreSQL
- **認證**: Supabase Auth
- **即時同步**: Supabase Realtime

## 📁 專案結構

```
src/
├── components/          # 可重用組件
│   ├── ui/             # 基礎 UI 組件
│   ├── Auth/           # 認證相關組件
│   ├── Player/         # 播放器組件
│   ├── ChannelList/    # 頻道列表組件
│   └── QRScanner/      # QR 掃描組件
├── pages/              # 頁面組件
│   ├── Home/           # 首頁
│   ├── Login/          # 登入頁面
│   ├── Register/       # 註冊頁面
│   ├── ChannelList/    # 頻道列表頁
│   ├── Player/         # 播放頁面
│   └── Settings/       # 設定頁面
├── services/           # 業務邏輯服務
│   ├── m3uParser.ts    # M3U 解析服務
│   ├── storage.ts      # 本地儲存服務
│   └── database.ts     # Supabase 資料庫服務
├── stores/             # 狀態管理
│   ├── appStore.ts     # 應用狀態
│   ├── authStore.ts    # 認證狀態
│   └── playerStore.ts  # 播放器狀態
├── config/             # 配置檔案
│   └── supabase.ts     # Supabase 配置
├── types/              # TypeScript 類型定義
└── utils/              # 工具函數
database/               # 資料庫相關
└── schema.sql          # 資料庫架構
```

## 🧪 測試

專案包含一個測試用的 M3U 檔案：
- 訪問: `http://localhost:5173/sample.m3u`
- 包含 3 個測試頻道用於功能驗證

## 🔧 開發

### 新增功能
1. 在對應的 `components/` 或 `pages/` 目錄下建立組件
2. 更新路由配置（如需要）
3. 更新狀態管理（如需要）

### 樣式開發
- 使用 TailwindCSS 工具類
- 自定義樣式在 `src/index.css` 中定義
- 響應式設計優先

## 📱 未來計劃

- [x] Supabase 雲端同步
- [x] 用戶認證系統
- [ ] 播放歷史記錄
- [ ] 頻道分享功能
- [ ] 即時聊天室
- [ ] Android APK 版本
- [ ] 離線播放功能
- [ ] 多語言支援
- [ ] 主題切換
- [ ] 推薦系統

## 📄 授權

MIT License
