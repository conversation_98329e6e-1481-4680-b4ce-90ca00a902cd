[build]
  command = "npm run build-netlify"
  publish = "dist"

# 設定 CORS 標頭來解決跨域問題（移動端優化）
[[headers]]
  for = "/*"
  [headers.values]
    Access-Control-Allow-Origin = "*"
    Access-Control-Allow-Methods = "GET, POST, PUT, DELETE, OPTIONS"
    Access-Control-Allow-Headers = "Content-Type, Authorization, X-Requested-With, Accept, Origin, Referer, User-Agent, Range, Cache-Control"
    Access-Control-Max-Age = "86400"
    Cross-Origin-Embedder-Policy = "unsafe-none"
    Cross-Origin-Opener-Policy = "same-origin"
    X-Frame-Options = "SAMEORIGIN"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"

# 針對 HLS 視頻文件的特殊 CORS 設定（移動端優化）
[[headers]]
  for = "/*.m3u8"
  [headers.values]
    Access-Control-Allow-Origin = "*"
    Access-Control-Allow-Methods = "GET, HEAD, OPTIONS"
    Access-Control-Allow-Headers = "Range, Content-Type, Accept, User-Agent, Referer"
    Content-Type = "application/x-mpegURL"
    Cache-Control = "public, max-age=300"

[[headers]]
  for = "/*.ts"
  [headers.values]
    Access-Control-Allow-Origin = "*"
    Access-Control-Allow-Methods = "GET, HEAD, OPTIONS"
    Access-Control-Allow-Headers = "Range, Content-Type, Accept, User-Agent, Referer"
    Content-Type = "video/mp2t"
    Cache-Control = "public, max-age=3600"

# 處理客戶端路由
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# 代理設定來處理 CORS 問題（如果需要）
[[redirects]]
  from = "/proxy/*"
  to = ":splat"
  status = 200
  force = true
  headers = {Access-Control-Allow-Origin = "*"}
